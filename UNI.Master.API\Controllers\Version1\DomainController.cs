using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Threading.Tasks;
using UNI.Master.BLL.Interfaces;
using UNI.Master.Model.Common;
using UNI.Master.Model.Domains;
using UNI.Model;
using UNI.Model.Api;

namespace UNI.Master.API.Controllers.Version1
{
    /// <summary>
    /// Domain Controller - Manages domain entities with full CRUD operations
    /// </summary>
    [ApiController]
    [Route("api/v1/[controller]/[action]")]
    public class DomainController : UniController
    {
        private readonly IDomainService _domainService;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="domainService">Domain service</param>
        /// <param name="appSettings">Application settings</param>
        /// <param name="logger">Logger factory</param>
        public DomainController(
            IDomainService domainService,
            IOptions<AppSettings> appSettings,
            ILoggerFactory logger) : base(appSettings, logger)
        {
            _domainService = domainService;
        }

        /// <summary>
        /// Get Domain Page - Retrieve paginated list of domains
        /// </summary>
        /// <param name="filter">Filter criteria for domains</param>
        /// <returns>Paginated list of domains</returns>
        [HttpGet]
        [ProducesResponseType(typeof(BaseResponse<CommonListPage>), 200)]
        public async Task<IActionResult> GetPage([FromQuery] DomainFilter filter)
        {
            filter.ucInput(this.UserId, this.ClientId, this.AcceptLanguage);
            var result = await _domainService.GetPageAsync(filter);
            var response = GetResponse(ApiResult.Success, result);
            return Ok(response);
        }

        /// <summary>
        /// Get Domain Info - Retrieve domain information by ID
        /// </summary>
        /// <param name="id">Domain ID</param>
        /// <returns>Domain information</returns>
        [HttpGet]
        [ProducesResponseType(typeof(BaseResponse<MasterCommonInfo>), 200)]
        public async Task<IActionResult> GetInfo([FromQuery] Guid? id)
        {
            try
            {
                var result = await _domainService.GetInfoAsync(id);
                var response = GetResponse(ApiResult.Success, result);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting domain info for ID: {Id}", id);
                var response = GetResponse<MasterCommonInfo>(ApiResult.Error, null, ex.Message);
                return BadRequest(response);
            }
        }

        /// <summary>
        /// Set Domain Info - Create or update domain information
        /// </summary>
        /// <param name="info">Domain information to save</param>
        /// <returns>Operation result</returns>
        [HttpPost]
        [ProducesResponseType(typeof(BaseResponse<bool>), 200)]
        public async Task<IActionResult> SetInfo([FromBody] MasterCommonInfo info)
        {
            try
            {
                var result = await _domainService.SetInfoAsync(info);
                var response = GetResponse(result.valid ? ApiResult.Success : ApiResult.Error, result.valid, result.messages);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting domain info");
                var response = GetResponse<bool>(ApiResult.Error, false, ex.Message);
                return BadRequest(response);
            }
        }

        /// <summary>
        /// Delete Domain - Remove domain by ID
        /// </summary>
        /// <param name="id">Domain ID to delete</param>
        /// <returns>Operation result</returns>
        [HttpDelete]
        [ProducesResponseType(typeof(BaseResponse<bool>), 200)]
        public async Task<IActionResult> Delete([FromQuery, Required] Guid? id)
        {
            try
            {
                var result = await _domainService.DeleteAsync(id);
                var response = GetResponse(result.valid ? ApiResult.Success : ApiResult.Error, result.valid, result.messages);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting domain with ID: {Id}", id);
                var response = GetResponse<bool>(ApiResult.Error, false, ex.Message);
                return BadRequest(response);
            }
        }
    }
}
