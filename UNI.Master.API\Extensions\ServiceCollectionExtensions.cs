using Dapper;
using Google.Apis.Auth.OAuth2;
using k8s;
using Microsoft.AspNetCore.Http;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Data;
using System.Net.Http;
using System.Text.Json;
using UNI.Common.CommonBase;
using UNI.Master.BLL.BusinessService;
using UNI.Master.BLL.BusinessService.Api;
using UNI.Master.BLL.BusinessService.Common;
using UNI.Master.BLL.BusinessService.Customer;
using UNI.Master.BLL.BusinessService.Deployments;
using UNI.Master.BLL.BusinessService.External;
using UNI.Master.BLL.BusinessService.k8s;
using UNI.Master.BLL.BusinessService.Products;
using UNI.Master.BLL.BusinessService.RegistryService;
using UNI.Master.BLL.Interfaces;
using UNI.Master.BLL.Interfaces.Api;
using UNI.Master.BLL.Interfaces.Common;
using UNI.Master.BLL.Interfaces.Customer;
using UNI.Master.BLL.Interfaces.Deployments;
using UNI.Master.BLL.Interfaces.External;
using UNI.Master.BLL.Interfaces.k8s;
using UNI.Master.BLL.Interfaces.Products;
using UNI.Master.BLL.Interfaces.Registry;
using UNI.Master.DAL.Interfaces;
using UNI.Master.DAL.Interfaces.Common;
using UNI.Master.DAL.Interfaces.Customer;
using UNI.Master.DAL.Interfaces.Deployments;
using UNI.Master.DAL.Interfaces.External;
using UNI.Master.DAL.Interfaces.Prodducts;
using UNI.Master.DAL.Interfaces.Products;
using UNI.Master.DAL.Interfaces.Registry;
using UNI.Master.DAL.Interfaces.Tenants;
using UNI.Master.DAL.Repositories;
using UNI.Master.DAL.Repositories.Common;
using UNI.Master.DAL.Repositories.Customer;
using UNI.Master.DAL.Repositories.Deployments;
using UNI.Master.DAL.Repositories.External;
using UNI.Master.DAL.Repositories.Products;
using UNI.Master.BLL.Interfaces.Customer;
using UNI.Master.BLL.BusinessService.Customer;
using UNI.Master.DAL.Interfaces.Customer;
using UNI.Master.DAL.Repositories.Customer;
using UNI.Master.BLL.Interfaces.Database;
using UNI.Master.BLL.BusinessService.Database;
using UNI.Master.DAL.Interfaces.Database;
using UNI.Master.DAL.Repositories.Database;
using UNI.Master.BLL.Interfaces.Database;
using UNI.Master.BLL.BusinessService.Database;
using UNI.Master.DAL.Repositories.Registry;
using UNI.Master.DAL.Repositories.Tenants;
using UNI.Model;
using UNI.Utilities.Keycloak.Extensions;

namespace UNI.Master.API.Extensions
{
    /// <summary>
    /// 
    /// </summary>
    public static class ServiceCollectionExtensions
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        /// <returns></returns>
        public static IServiceCollection RegisterServices(this IServiceCollection services, IConfiguration configuration)
        {
            //keycloak client
            var keycloakConfig = configuration.GetExternalService<UNI.Utilities.Keycloak.Models.Settings>("Keycloak");
            services.AddKeyCloakClient(keycloakConfig);

            // and a lot more Services
            services.AddSingleton(configuration);

            AddStorageService(services, configuration);
            
            services.AddScoped<IUniCommonBaseRepository, UniCommonBaseRepository>();

            services.AddScoped<IAccountService, AccountService>();
            services.AddScoped<IAccountRepository, AccountRepository>();

            services.AddScoped<IAddressService, AddressService>();
            services.AddScoped<IAddressRepository, AddressRepository>();

            services.AddScoped<ICommonService, CommonService>();
            services.AddScoped<ICommonRepository, CommonRepository>();

            services.AddScoped<ICustomerService, CustomerService>();
            services.AddScoped<ICustomerRepository, CustomerRepository>();

            services.AddScoped<ICustomerRegService, CustomerRegService>();
            services.AddScoped<ICustomerRegRepository, CustomerRegRepository>();

            services.AddScoped<IDatabaseService, DatabaseService>();
            services.AddScoped<IDatabaseRepository, DatabaseRepository>();

            services.AddScoped<IDatabaseInstanceService, DatabaseInstanceService>();
            services.AddScoped<IDatabaseInstanceRepository, DatabaseInstanceRepository>();

            services.AddScoped<IProductService, ProductService>();
            services.AddScoped<IProductRepository, ProductRepository>();

            services.AddScoped<IProductLineService, ProductLineService>();
            services.AddScoped<IProductLineRepository, ProductLineRepository>();

            services.AddScoped<IProductFeatureService, ProductFeatureService>();
            services.AddScoped<IProductFeatureRepository, ProductFeatureRepository>();

            services.AddScoped<IProductPackageService, ProductPackageService>();
            services.AddScoped<IProductPackageRepository, ProductPackageRepository>();

            services.AddScoped<IOrderService, OrderService>();
            services.AddScoped<IOrderDetailRepository, OrderDetailRepository>();

            services.AddScoped<IOrderDetailService, OrderDetailService>();
            services.AddScoped<IOrderRepository, OrderRepository>();

            services.AddScoped<IContractService, ContractService>();
            services.AddScoped<IContractRepository, ContractRepository>();

            services.AddScoped<IOrderAccountService, OrderAccountService>();
            services.AddScoped<IOrderAccountRepository, OrderAccountRepository>();

            services.AddScoped<IWorkflowService, WorkflowService>();
            services.AddScoped<IWorkflowRepository, WorkflowRepository>();

            services.AddScoped<IUIConfigService, UIConfigService>();
            services.AddScoped<IUiConfigRepository, UIConfigRepository>();

            services.AddScoped<IDashBoardService, DashboardService>();
            services.AddScoped<IDashBoardRepository, DashboardRepository>();

            services.AddScoped<IOrganizationService, OrganizationService>();
            services.AddScoped<IOrganizationRepository, OrganizationRepository>();

            services.AddScoped<IDeviceRepository, DeviceRepository>();


            services.AddScoped<ICustomerService, CustomerService>();
            services.AddScoped<ICustomerRepository, CustomerRepository>();

            services.AddScoped<IConsultancyRegistrationService, ConsultancyRegistrationService>();
            services.AddScoped<IConsultancyRegistrationRepository, ConsultancyRegistrationRepository>();

            services.AddScoped<ITenantService, TenantService>();
            services.AddScoped<ITenantRepository, TenantRepository>();

            services.AddScoped<IRegistryService, RegistryService>();
            services.AddScoped<IRegistryRepository, RegistryRepository>();

            services.AddScoped<IProductComponentService, ProductComponentService>();
            services.AddScoped<IProductComponentRepository, ProductComponentRepository>();

            services.AddScoped<IProductModuleService, ProductModuleService>();
            services.AddScoped<IProductModuleRepository, ProductModuleRepository>();

            services.AddScoped<IResourceTemplateService, ResourceTemplateService>();
            services.AddScoped<IResourceTemplateRepository, ResourceTemplateRepository>();

            services.AddScoped<ICustomerProductService, CustomerProductService>();
            services.AddScoped<ICustomerProductRepository, CustomerProductRepository>();

            services.AddScoped<IDeploymentService, DeploymentService>();
            services.AddScoped<IDeploymentRepository, DeploymentRepository>();

            services.AddScoped<IDeploymentDatabaseService, DeploymentDatabaseService>();
            services.AddScoped<IDeploymentDatabaseRepository, DeploymentDatabaseRepository>();

            services.AddScoped<IExternalService, ExternalService>();
            services.AddScoped<IExternalRepository, ExternalRepository>();

            services.AddScoped<IDomainService, DomainService>();
            services.AddScoped<IDomainRepository, DomainRepository>();

            services.AddTransient<HttpClient>();
            services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
            //services.AddSingleton(typeof(RestClient), new RestClient { });

            services.AddK8SService();
            services.AddHarborRegistry(configuration);
            
            services.AddScopedUniBaseService(ServiceLifetime.Scoped, "dbUniMasterConnection", "sp_common_filter", false);
            return services;
        }

        private static void AddHarborRegistry(this IServiceCollection services, IConfiguration configuration)
        {
            var harBorConfig = configuration.GetExternalService("Harbor");
            var baseUrl = harBorConfig["BaseUrl"];
            var token = harBorConfig["Credentials:Token"];
            if (baseUrl != null && token != null)
                services.AddSingleton(new HarborClient.HarborClient(baseUrl, token));
        }

        private static void AddK8SService(this IServiceCollection services)
        {
            services.AddSingleton<IKubernetes>(new Kubernetes(KubernetesClientConfiguration.BuildDefaultConfig()));
            services.AddScoped<IKubernetesService, KubernetesService>();
        }
        static void AddStorageService(this IServiceCollection services,
            IConfiguration configuration)
        {
            var storageProvider = configuration["StorageService:Provider"];
            //save storage url to config db
            var serviceProvider = services.BuildServiceProvider();
            //var commonRepository = serviceProvider.GetRequiredService<ICommonRepository>();
            //_ = new CommonRepository(configuration).SetConfigData("api_storage_url",
            //    configuration["StorageService:MinIo:ProxyEndpoint"]);
            SetConfigData(configuration.GetConnectionString("dbUniMasterConnection"), "api_storage_url", configuration["StorageService:MinIo:ProxyEndpoint"]);
            //map storage service
            if (storageProvider == "MinIo")
            {
                services.AddSingleton<IApiStorageService, ApiMinIoStorageService>(sp =>
                    new ApiMinIoStorageService(
                        sp.GetRequiredService<ILogger<ApiMinIoStorageService>>(),
                        new StorageConfig()
                        {
                            AccessKey = configuration["StorageService:MinIo:AccessKey"],
                            SecretKey = configuration["StorageService:MinIo:SecretKey"],
                            Endpoint = configuration["StorageService:MinIo:Endpoint"],
                            BucketName = configuration["StorageService:MinIo:BucketName"],
                            Region = configuration["StorageService:MinIo:Region"],
                            UseSsl = bool.Parse(configuration["StorageService:MinIo:UseSSL"] ?? "true"),
                            ProxyEndpoint = configuration["StorageService:MinIo:ProxyEndpoint"],
                            PrefixFolder = configuration["StorageService:MinIo:PrefixFolder"]
                        }));
            }
            else
            {
                var googleCredential = GoogleCredentialJson(configuration);
                services.AddSingleton<IApiStorageService, ApiFireBaseStorageService>(sp =>
                    new ApiFireBaseStorageService(
                        sp.GetRequiredService<ILogger<ApiFireBaseStorageService>>(),
                        new StorageConfig()
                        {
                            AccessKey = configuration["StorageService:Firebase:AccessKey"],
                            SecretKey = configuration["StorageService:Firebase:SecretKey"],
                            Endpoint = configuration["StorageService:Firebase:Endpoint"],
                            BucketName = configuration["StorageService:Firebase:BucketName"],
                            Region = configuration["StorageService:Firebase:Region"],
                            UseSsl = bool.Parse(configuration["StorageService:Firebase:UseSSL"] ?? "true"),
                            ProxyEndpoint = configuration["StorageService:Firebase:ProxyEndpoint"],
                            PrefixFolder = configuration["StorageService:Firebase:PrefixFolder"]
                        }, googleCredential));
            }
        }
        private static GoogleCredential GoogleCredentialJson(IConfiguration configuration)
        {
            // Đọc GoogleCredential vào đối tượng Dictionary
            var googleCredentialSection = configuration.GetSection("GoogleCredential");
            if (!googleCredentialSection.Exists())
            {
                //Log.Warning("googleCredentialSection not exists");
                return null;
            }

            var googleCredential = googleCredentialSection.Get<Dictionary<string, string>>();

            if (googleCredential == null || googleCredential.Count == 0)
            {
                //Log.Warning("googleCredentialSection is empty");
                return null;
            }

            // Chuyển đổi đối tượng thành chuỗi JSON
            var googleCredentialJson = JsonSerializer.Serialize(googleCredential, new JsonSerializerOptions
            {
                WriteIndented = true // Format dễ đọc
            });
            return GoogleCredential.FromJson(googleCredentialJson);
        }
        private static void SetConfigData(string connectionString, string key, string value)
        {
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();
                connection.Execute("sp_config_data_set", new { key, value }, commandType: CommandType.StoredProcedure);
            }
            return;
        }
    }
}
