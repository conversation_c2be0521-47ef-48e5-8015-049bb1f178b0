-- =============================================
-- Author: System Generated
-- Create date: 2025-01-06
-- Description: Domain - Delete domain record
-- =============================================
CREATE PROCEDURE [dbo].[sp_domain_delete]
    @userId NVARCHAR(50),
    @id UNIQUEIDENTIFIER
AS
BEGIN TRY
    DECLARE @valid BIT = 0,
            @messages NVARCHAR(250);

    -- Check if record exists
    IF NOT EXISTS (SELECT 1 FROM dbo.[domain] WHERE id = @id)
    BEGIN
        SET @messages = N'Bản ghi không tồn tại';
        GOTO FINAL;
    END;

    -- Check for dependencies (add your business rules here)
    -- Example: Check if domain is being used in other tables
    /*
    IF EXISTS (SELECT 1 FROM dbo.[other_table] WHERE domain_id = @id)
    BEGIN
        SET @messages = N'Domain đang được sử dụng. Không thể xóa';
        GOTO FINAL;
    END;
    */

    -- Delete the record
    DELETE FROM dbo.domain
    WHERE id = @id;
    
    SET @valid = 1;
    SET @messages = N'Xóa thành công';
    
    FINAL:
    SELECT @valid valid,
           @messages AS [messages];

END TRY
BEGIN CATCH
    SELECT 0 AS valid, ERROR_MESSAGE() AS [messages];
    
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_domain_delete' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();
    SET @AddlInfo = '@UserId: ' + ISNULL(@userId, 'NULL') + ', @id: ' + ISNULL(CAST(@id AS NVARCHAR(50)), 'NULL');

    EXEC utl_errorlog_set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'domain',
                          'DEL',
                          @SessionID,
                          @AddlInfo;
END CATCH;
GO
