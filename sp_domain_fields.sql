-- =============================================
-- Author: System Generated
-- Create date: 2025-01-06
-- Description: Domain - Get domain fields for form
-- Output: Returns form configuration and field data
-- =============================================
CREATE PROCEDURE [dbo].[sp_domain_fields]
    @userId NVARCHAR(50) = NULL,
    @id UNIQUEIDENTIFIER = NULL,
    @acceptLanguage NVARCHAR(50) = 'vi-VN'
AS
BEGIN TRY
    DECLARE @tableKey VARCHAR(50) = 'domain'
    DECLARE @groupKey VARCHAR(50) = 'mas_domain_group_update'

    -- 1. Return basic info
    SELECT @id oid,
           tableKey = @tableKey,
           groupKey = @groupKey

    -- 2. Return field groups
    SELECT *
    FROM [dbo].[fn_get_field_group](@groupKey, @acceptLanguage)
    ORDER BY intOrder

    -- 3. Get domain data into temp table
    SELECT a.*
    INTO #domain
    FROM domain a
    WHERE a.id = @id

    -- If new record (id is null), create empty temp table with structure
    IF @id IS NULL
    BEGIN
        DELETE FROM #domain;
        INSERT INTO #domain (id, [type], [code], [name])
        VALUES (NULL, '', '', '');
    END

    -- 4. Return field configuration with data
    EXEC sp_config_data_fields @id = @id,
                               @tableName = @tableKey,
                               @dataTableName = '#domain',
                               @acceptLanguage = @acceptLanguage

END TRY
BEGIN CATCH
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_domain_fields' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();
    SET @AddlInfo = ' ';

    EXEC utl_ErrorLog_Set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          @tableKey,
                          'GET',
                          @SessionID,
                          @AddlInfo;
END CATCH;
GO
