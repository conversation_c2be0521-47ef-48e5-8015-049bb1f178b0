-- =============================================
-- Author:		System Generated
-- Create date: 2025-01-06
-- Description:	Domain - Get paginated list of domains
-- =============================================
CREATE PROCEDURE [dbo].[sp_domain_page] 
    @userId UNIQUEIDENTIFIER = NULL,
    @clientId NVARCHAR(50) = NULL,
    @type NVARCHAR(100) = NULL,
    @code NVARCHAR(100) = NULL,
    @name NVARCHAR(255) = NULL,
    @filter NVARCHAR(255) = NULL,
    @Offset INT = 0,
    @PageSize INT = 10,
    @gridWidth INT = 0,
    @gridKey NVARCHAR(100) = NULL OUT,
    @Total INT = 0 OUT,
    @TotalFiltered INT = 0 OUT,
    @acceptLanguage NVARCHAR(50) = 'vi-VN'
AS
BEGIN TRY
    SET @gridKey = 'domain_page'
    SET @Offset = ISNULL(@Offset, 0);
    SET @PageSize = ISNULL(@PageSize, 10);
    SET @Total = ISNULL(@Total, 0);
    SET @filter = ISNULL(@filter, '');
    SET @type = ISNULL(@type, '');
    SET @code = ISNULL(@code, '');
    SET @name = ISNULL(@name, '');

    -- Validate pagination parameters
    IF @PageSize <= 0
        SET @PageSize = 10;

    IF @Offset < 0
        SET @Offset = 0;

    -- Build WHERE clause for filtering
    DECLARE @whereClause NVARCHAR(MAX) = ' WHERE 1=1 ';
    
    IF @type <> ''
        SET @whereClause = @whereClause + ' AND ([type] LIKE ''%' + @type + '%'') ';
    
    IF @code <> ''
        SET @whereClause = @whereClause + ' AND ([code] LIKE ''%' + @code + '%'') ';
    
    IF @name <> ''
        SET @whereClause = @whereClause + ' AND ([name] LIKE ''%' + @name + '%'') ';
    
    IF @filter <> ''
        SET @whereClause = @whereClause + ' AND ([code] LIKE ''%' + @filter + '%'' OR [name] LIKE ''%' + @filter + '%'') ';

    -- Get total count
    DECLARE @countSql NVARCHAR(MAX) = 'SELECT @Total = COUNT(1) FROM [domain] ' + @whereClause;
    EXEC sp_executesql @countSql, N'@Total INT OUTPUT', @Total OUTPUT;

    SET @TotalFiltered = @Total;

    -- Return grid configuration on first page
    IF @Offset = 0
    BEGIN
        SELECT *
        FROM [dbo].fn_config_list_gets(@gridKey, 0, @acceptLanguage)
        ORDER BY [ordinal];
    END;

    -- Get paginated data
    DECLARE @dataSql NVARCHAR(MAX) = '
        SELECT 
            [id],
            [type],
            [code],
            [name],
            ROW_NUMBER() OVER (ORDER BY [name], [code]) AS stt
        FROM [domain] ' + @whereClause + '
        ORDER BY [name], [code] 
        OFFSET ' + CAST(@Offset AS NVARCHAR(10)) + ' ROWS
        FETCH NEXT ' + CAST(@PageSize AS NVARCHAR(10)) + ' ROWS ONLY';

    EXEC sp_executesql @dataSql;

END TRY
BEGIN CATCH
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();
    SET @AddlInfo = '';

    EXEC utl_errorlog_set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'domain',
                          'GET',
                          @SessionID,
                          @AddlInfo;
END CATCH
GO
