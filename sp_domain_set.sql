-- =============================================
-- Author: System Generated
-- Create date: 2025-01-06
-- Description: Domain - Create or update domain record
-- =============================================
CREATE PROCEDURE [dbo].[sp_domain_set]
    @userId NVARCHAR(450) = NULL,
    @id UNIQUEIDENTIFIER = NULL,
    @type NVARCHAR(100) = NULL,
    @code NVARCHAR(100) = NULL,
    @name NVARCHAR(255) = NULL
AS
BEGIN TRY
    DECLARE @valid BIT = 0,
            @messages NVARCHAR(250);

    -- Validate required fields
    IF @code IS NULL OR LTRIM(RTRIM(@code)) = ''
    BEGIN
        SET @messages = N'Mã domain không được để trống';
        GOTO FINAL;
    END

    IF @name IS NULL OR LTRIM(RTRIM(@name)) = ''
    BEGIN
        SET @messages = N'Tên domain không được để trống';
        GOTO FINAL;
    END

    -- Check for duplicate code (exclude current record if updating)
    IF EXISTS (SELECT 1 FROM domain WHERE [code] = @code AND (@id IS NULL OR id <> @id))
    BEGIN
        SET @messages = N'Mã domain đã tồn tại';
        GOTO FINAL;
    END

    IF (@id IS NULL)
    BEGIN
        -- INSERT new record
        SET @id = NEWID();
        
        INSERT INTO dbo.[domain]
        (
            id,
            [type],
            [code],
            [name],
            created_date,
            created_by
        )
        VALUES
        (   
            @id,
            @type,
            @code,
            @name,
            GETDATE(),
            @userId
        )
        
        SET @valid = 1;
        SET @messages = N'Thêm mới thành công';
    END
    ELSE
    BEGIN
        -- UPDATE existing record
        IF NOT EXISTS (SELECT 1 FROM domain WHERE id = @id)
        BEGIN
            SET @messages = N'Bản ghi không tồn tại';
            GOTO FINAL;
        END

        UPDATE dbo.[domain]
        SET [type] = @type,
            [code] = @code,
            [name] = @name,
            updated_date = GETDATE(),
            updated_by = @userId
        WHERE id = @id;
        
        SET @valid = 1;
        SET @messages = N'Cập nhật thành công';
    END

    FINAL:
    SELECT @valid valid,
           @messages AS [messages];

END TRY
BEGIN CATCH
    SELECT 0 AS valid, ERROR_MESSAGE() AS [messages];
    
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_domain_set' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();
    SET @AddlInfo = '@UserId: ' + ISNULL(@userId, 'NULL') + ', @id: ' + ISNULL(CAST(@id AS NVARCHAR(50)), 'NULL');

    EXEC utl_ErrorLog_Set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'domain',
                          'SET',
                          @SessionID,
                          @AddlInfo;
END CATCH;
GO
